import { cognitoConfig } from '../../../utils/cognitoConfig';

// Helper to generate PKCE random string
function generateRandomString(length: number): string {
  const charset =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
  let result = '';
  const values = new Uint32Array(length);
  window.crypto.getRandomValues(values);
  for (let i = 0; i < length; i++) {
    result += charset[values[i] % charset.length];
  }
  return result;
}

// Helper to generate SHA-256 hash
async function sha256(plain: string): Promise<ArrayBuffer> {
  const encoder = new TextEncoder();
  const data = encoder.encode(plain);
  return window.crypto.subtle.digest('SHA-256', data);
}

// Helper for Base64URL encoding
function base64urlencode(a: ArrayBuffer): string {
  let str = '';
  const bytes = new Uint8Array(a);
  const len = bytes.byteLength;
  for (let i = 0; i < len; i++) {
    str += String.fromCharCode(bytes[i]);
  }
  return btoa(str).replace(/\+/g, '-').replace(/\//g, '_').replace(/=+$/, '');
}

/**
 * Clear all Cognito-related cookies
 * CRITICAL: Cookies are stored at domain level and persist across tabs
 * This is the main cause of "Invalid challenge transition" errors
 */
export function clearCognitoCookies() {
  if (typeof document === 'undefined') return;

  // Get all cookies
  const cookies = document.cookie.split(';');

  // Clear each cookie that might be related to Cognito
  cookies.forEach((cookie) => {
    const cookieName = cookie.split('=')[0].trim();

    // Clear the cookie for all possible paths and domains
    const clearCookie = (name: string) => {
      // Clear for current domain
      document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;

      // Clear for root domain (if subdomain)
      const domain = window.location.hostname;
      const parts = domain.split('.');
      if (parts.length > 2) {
        const rootDomain = parts.slice(-2).join('.');
        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.${rootDomain}`;
      }

      // Clear for specific Cognito domain
      document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.amazoncognito.com`;
      document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.auth.amazoncognito.com`;
    };

    // Clear all cookies (Cognito cookies might have various names)
    clearCookie(cookieName);
  });

  console.log('Admin: Cleared all Cognito cookies');
}

/**
 * Initiates Cognito login with proper session isolation
 *
 * CRITICAL FIX for "Invalid challenge transition" error:
 * - Clears Cognito server-side session by calling logout endpoint first
 * - Clears all Cognito-related cookies and storage before starting new flow
 * - Uses fresh PKCE verifier and state for each login attempt
 * - Adds nonce parameter to prevent Cognito from reusing cached OAuth state
 */
export async function loginWithCognito() {
  if (typeof window === 'undefined') return;

  // 1. CRITICAL: Clear Cognito cookies first (domain-level, persists across tabs)
  // This is the PRIMARY cause of "Invalid challenge transition" errors
  clearCognitoCookies();

  // 2. CRITICAL: Clear Cognito server-side session by calling logout endpoint
  // This clears cookies on Cognito's domain (which we can't access from our domain)
  const cognitoDomain = cognitoConfig.domain;
  const cognitoClientId = cognitoConfig.clientId;

  if (cognitoDomain && cognitoClientId) {
    try {
      let domainUrl = cognitoDomain;
      if (!domainUrl.startsWith('http')) {
        domainUrl = `https://${domainUrl}`;
      }
      if (domainUrl.endsWith('/')) {
        domainUrl = domainUrl.slice(0, -1);
      }

      // Call Cognito logout endpoint silently in an iframe to clear server-side session
      // This clears Cognito's cookies without redirecting the user
      const logoutUrl = `${domainUrl}/logout?client_id=${cognitoClientId}&logout_uri=${encodeURIComponent(`${window.location.origin}/login`)}`;

      // Create hidden iframe to call logout endpoint
      const iframe = document.createElement('iframe');
      iframe.style.display = 'none';
      iframe.src = logoutUrl;
      document.body.appendChild(iframe);

      // Wait for logout to complete, then remove iframe
      await new Promise((resolve) => setTimeout(resolve, 500));
      document.body.removeChild(iframe);

      console.log('Admin: Cleared Cognito server-side session');
    } catch (error) {
      console.warn(
        'Admin: Failed to clear Cognito server-side session:',
        error
      );
      // Continue anyway - the nonce parameter should still help
    }
  }

  // 3. CRITICAL: Clear ANY Cognito keys from BOTH storage areas before starting
  // This ensures a completely fresh session state and prevents 'Invalid challenge transition'
  const keysToClear = ['Cognito', 'amplify', 'cognito'];

  keysToClear.forEach((token) => {
    Object.keys(localStorage)
      .filter((key) => key.toLowerCase().includes(token.toLowerCase()))
      .forEach((key) => localStorage.removeItem(key));

    Object.keys(sessionStorage)
      .filter((key) => key.toLowerCase().includes(token.toLowerCase()))
      .forEach((key) => sessionStorage.removeItem(key));
  });

  // 4. PKCE & State Setup
  const verifier = generateRandomString(128);
  const challengeBuffer = await sha256(verifier);
  const challenge = base64urlencode(challengeBuffer);
  const state = generateRandomString(32);
  const nonce = generateRandomString(32); // Add nonce for additional security

  // Store in sessionStorage for the callback
  sessionStorage.setItem('cognito_verifier', verifier);
  sessionStorage.setItem('cognito_state', state);

  // 5. Construct Cognito Hosted UI URL
  const scope = cognitoConfig.scopes || 'openid email profile';
  const redirectUri = cognitoConfig.redirectUri;
  const clientId = cognitoConfig.clientId;
  const domain = cognitoConfig.domain;

  const params = new URLSearchParams({
    response_type: 'code',
    client_id: clientId || '',
    redirect_uri: redirectUri || '',
    scope,
    state,
    nonce, // CRITICAL: Prevents Cognito from reusing cached OAuth state
    // Use 'login' to force fresh authentication and clear any server-side session state
    prompt: 'login',
    code_challenge: challenge,
    code_challenge_method: 'S256',
  });

  let domainUrl = domain || '';
  if (!domainUrl.startsWith('http')) {
    domainUrl = `https://${domainUrl}`;
  }
  if (domainUrl.endsWith('/')) {
    domainUrl = domainUrl.slice(0, -1);
  }

  console.log(
    'Admin: Initiating tab-isolated login redirect to Cognito with fresh OAuth state...'
  );
  window.location.href = `${domainUrl}/oauth2/authorize?${params.toString()}`;
}
